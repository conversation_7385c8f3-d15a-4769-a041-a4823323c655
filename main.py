#!/usr/bin/env python3
"""
EduGuideBot Main Entry Point
Simple launcher for the Telegram bot
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def main():
    """Main entry point"""
    # Check for bot token
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("BOT_TOKEN environment variable not set!")
        logger.info("Please set your bot token: export BOT_TOKEN='your_token_here'")
        return 1
    
    logger.info("🚀 Starting EduGuideBot...")
    logger.info(f"Bot Token: {bot_token[:10]}...")
    
    try:
        # Import and run the bot
        from src.bot.app import main as bot_main
        bot_main()
        
    except KeyboardInterrupt:
        logger.info("Bo<PERSON> stopped by user")
        return 0
    except Exception as e:
        logger.error(f"Bot crashed: {e}", exc_info=True)
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
